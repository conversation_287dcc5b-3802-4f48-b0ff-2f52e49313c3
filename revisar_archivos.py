#!/usr/bin/env python3
"""
Ejemplos de cómo revisar todos los archivos en una carpeta y subdirectorios
usando pathlib.Path
"""

from pathlib import Path
import os


def revisar_archivos_recursivo(carpeta_path):
    """
    Revisa todos los archivos en una carpeta y subdirectorios recursivamente
    
    Args:
        carpeta_path (str): Ruta de la carpeta a revisar
    """
    carpeta = Path(carpeta_path)
    
    if not carpeta.exists():
        print(f"La carpeta {carpeta_path} no existe")
        return
    
    if not carpeta.is_dir():
        print(f"{carpeta_path} no es una carpeta")
        return
    
    print(f"Revisando archivos en: {carpeta.absolute()}")
    print("-" * 50)
    
    # Método 1: Usar rglob() para buscar recursivamente
    print("=== Método 1: Usando rglob('*') ===")
    for archivo in carpeta.rglob('*'):
        if archivo.is_file():
            print(f"Archivo: {archivo}")
            print(f"  - Tamaño: {archivo.stat().st_size} bytes")
            print(f"  - Extensión: {archivo.suffix}")
            print(f"  - Nombre: {archivo.name}")
            print(f"  - Directorio padre: {archivo.parent}")
            print()


def revisar_archivos_por_extension(carpeta_path, extension="*"):
    """
    Revisa archivos por extensión específica
    
    Args:
        carpeta_path (str): Ruta de la carpeta
        extension (str): Extensión a buscar (ej: "*.py", "*.txt")
    """
    carpeta = Path(carpeta_path)
    
    print(f"=== Buscando archivos {extension} ===")
    for archivo in carpeta.rglob(extension):
        if archivo.is_file():
            print(f"Encontrado: {archivo}")


def revisar_con_iterdir_recursivo(carpeta_path, nivel=0):
    """
    Revisa archivos usando iterdir() de forma recursiva
    
    Args:
        carpeta_path (Path): Ruta de la carpeta
        nivel (int): Nivel de profundidad (para indentación)
    """
    carpeta = Path(carpeta_path)
    indentacion = "  " * nivel
    
    try:
        for item in carpeta.iterdir():
            if item.is_file():
                print(f"{indentacion}📄 {item.name} ({item.stat().st_size} bytes)")
            elif item.is_dir():
                print(f"{indentacion}📁 {item.name}/")
                # Llamada recursiva para subdirectorios
                revisar_con_iterdir_recursivo(item, nivel + 1)
    except PermissionError:
        print(f"{indentacion}❌ Sin permisos para acceder a {carpeta}")


def obtener_estadisticas_carpeta(carpeta_path):
    """
    Obtiene estadísticas de la carpeta
    
    Args:
        carpeta_path (str): Ruta de la carpeta
    """
    carpeta = Path(carpeta_path)
    
    archivos = []
    directorios = []
    total_tamaño = 0
    
    for item in carpeta.rglob('*'):
        if item.is_file():
            archivos.append(item)
            total_tamaño += item.stat().st_size
        elif item.is_dir():
            directorios.append(item)
    
    print(f"=== Estadísticas de {carpeta_path} ===")
    print(f"Total de archivos: {len(archivos)}")
    print(f"Total de directorios: {len(directorios)}")
    print(f"Tamaño total: {total_tamaño:,} bytes ({total_tamaño / 1024 / 1024:.2f} MB)")
    
    # Agrupar por extensión
    extensiones = {}
    for archivo in archivos:
        ext = archivo.suffix.lower() or "sin_extension"
        extensiones[ext] = extensiones.get(ext, 0) + 1
    
    print("\nArchivos por extensión:")
    for ext, count in sorted(extensiones.items()):
        print(f"  {ext}: {count} archivo(s)")


def buscar_archivos_con_filtro(carpeta_path, filtro_func=None):
    """
    Busca archivos aplicando un filtro personalizado
    
    Args:
        carpeta_path (str): Ruta de la carpeta
        filtro_func (callable): Función que recibe un Path y retorna bool
    """
    carpeta = Path(carpeta_path)
    
    if filtro_func is None:
        # Filtro por defecto: archivos Python
        filtro_func = lambda p: p.suffix == '.py'
    
    print("=== Archivos que cumplen el filtro ===")
    for archivo in carpeta.rglob('*'):
        if archivo.is_file() and filtro_func(archivo):
            print(f"✅ {archivo}")


def ejemplo_filtros():
    """Ejemplos de diferentes filtros"""
    carpeta = "."
    
    print("Filtro 1: Archivos Python")
    buscar_archivos_con_filtro(carpeta, lambda p: p.suffix == '.py')
    
    print("\nFiltro 2: Archivos grandes (>1KB)")
    buscar_archivos_con_filtro(carpeta, lambda p: p.stat().st_size > 1024)
    
    print("\nFiltro 3: Archivos que contienen 'test' en el nombre")
    buscar_archivos_con_filtro(carpeta, lambda p: 'test' in p.name.lower())


if __name__ == "__main__":
    # Carpeta a revisar (puedes cambiar esta ruta)
    carpeta_objetivo = "."  # Carpeta actual
    
    print("🔍 REVISIÓN COMPLETA DE ARCHIVOS")
    print("=" * 60)
    
    # Revisar todos los archivos
    revisar_archivos_recursivo(carpeta_objetivo)
    
    print("\n" + "=" * 60)
    
    # Revisar estructura con iterdir
    print("=== Estructura de directorios ===")
    revisar_con_iterdir_recursivo(carpeta_objetivo)
    
    print("\n" + "=" * 60)
    
    # Obtener estadísticas
    obtener_estadisticas_carpeta(carpeta_objetivo)
    
    print("\n" + "=" * 60)
    
    # Buscar archivos específicos
    revisar_archivos_por_extension(carpeta_objetivo, "*.py")
    
    print("\n" + "=" * 60)
    
    # Ejemplos de filtros
    ejemplo_filtros()
