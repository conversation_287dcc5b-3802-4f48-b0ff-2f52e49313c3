#!/usr/bin/env python3
"""
Función optimizada para buscar archivos por coincidencias parciales en el nombre
"""

from pathlib import Path

def buscar_archivos_por_nombres_parciales(carpeta, nombres_buscados, incluir_subcarpetas=True, 
                                        case_sensitive=False, mostrar_detalles=True):
    """
    Busca archivos que contengan parte del nombre especificado
    
    Args:
        carpeta (str o Path): Ruta de la carpeta donde buscar
        nombres_buscados (list): Lista de nombres parciales a buscar
        incluir_subcarpetas (bool): Si buscar en subdirectorios recursivamente
        case_sensitive (bool): Si la búsqueda debe ser sensible a mayúsculas
        mostrar_detalles (bool): Si mostrar información detallada durante la búsqueda
        
    Returns:
        dict: Diccionario con los resultados organizados por término de búsqueda
    """
    carpeta = Path(carpeta)
    
    if not carpeta.exists():
        print(f"❌ La carpeta '{carpeta}' no existe")
        return {}
    
    if mostrar_detalles:
        print(f"🔍 Buscando en: {carpeta.absolute()}")
        print(f"📝 Términos: {nombres_buscados}")
        print(f"📁 Incluir subcarpetas: {incluir_subcarpetas}")
        print(f"🔤 Sensible a mayúsculas: {case_sensitive}")
        print("-" * 60)
    
    resultados = {nombre: [] for nombre in nombres_buscados}
    
    # Elegir el método de búsqueda según si incluir subcarpetas o no
    if incluir_subcarpetas:
        archivos = carpeta.rglob('*')  # Búsqueda recursiva
    else:
        archivos = carpeta.glob('*')   # Solo en la carpeta actual
    
    # Buscar en todos los archivos
    for archivo in archivos:
        if archivo.is_file():
            # Omitir archivos temporales que empiecen con ~
            if archivo.name.startswith('~'):
                continue

            # Verificar cada término de búsqueda
            for nombre_buscado in nombres_buscados:
                # Aplicar case sensitivity según el parámetro
                archivo_nombre = archivo.name if case_sensitive else archivo.name.lower()
                busqueda_nombre = nombre_buscado if case_sensitive else nombre_buscado.lower()

                if busqueda_nombre in archivo_nombre:
                    if mostrar_detalles:
                        print(f"✅ '{nombre_buscado}' → {archivo}")
                    resultados[nombre_buscado].append(archivo)
    
    # Mostrar resumen
    if mostrar_detalles:
        print("\n" + "=" * 60)
        print("📊 RESUMEN:")
        total_encontrados = 0
        for termino, archivos in resultados.items():
            count = len(archivos)
            total_encontrados += count
            print(f"'{termino}': {count} archivo(s)")
        print(f"\nTotal de archivos encontrados: {total_encontrados}")
    
    return resultados

def ejemplo_uso_simple():
    """Ejemplo simple de uso"""
    
    # Buscar archivos Python en la carpeta actual
    resultados = buscar_archivos_por_nombres_parciales(
        carpeta=".",
        nombres_buscados=["py", "firme"],
        incluir_subcarpetas=False,  # Solo en carpeta actual
        mostrar_detalles=True
    )
    
    # Acceder a los resultados
    archivos_py = resultados["py"]
    archivos_firme = resultados["firme"]
    
    print(f"\nArchivos Python encontrados: {len(archivos_py)}")
    for archivo in archivos_py:
        print(f"  - {archivo.name}")
    
    return resultados

def ejemplo_uso_avanzado():
    """Ejemplo más avanzado con múltiples opciones"""
    
    print("=== BÚSQUEDA AVANZADA ===")
    
    # Lista de términos a buscar
    terminos = ["ejemplo", "buscar", "firme", ".py"]
    
    # Búsqueda recursiva con detalles
    resultados = buscar_archivos_por_nombres_parciales(
        carpeta=".",
        nombres_buscados=terminos,
        incluir_subcarpetas=True,
        case_sensitive=False,
        mostrar_detalles=True
    )
    
    # Procesar resultados
    print("\n" + "="*60)
    print("ANÁLISIS DE RESULTADOS:")
    
    for termino, archivos in resultados.items():
        if archivos:  # Solo mostrar si hay resultados
            print(f"\n📁 Archivos que contienen '{termino}':")
            for archivo in archivos[:5]:  # Mostrar solo los primeros 5
                tamaño = archivo.stat().st_size
                print(f"  📄 {archivo.name} ({tamaño:,} bytes)")
                print(f"      📍 {archivo.parent}")
            
            if len(archivos) > 5:
                print(f"  ... y {len(archivos) - 5} más")
    
    return resultados

if __name__ == "__main__":
    print("🔍 BÚSQUEDA DE ARCHIVOS POR NOMBRES PARCIALES")
    print("=" * 60)
    
    # Ejemplo simple
    print("\n=== EJEMPLO SIMPLE ===")
    ejemplo_uso_simple()
    
    print("\n" + "="*80 + "\n")
    
    # Ejemplo avanzado
    ejemplo_uso_avanzado()
    
    print("\n" + "="*80)
    print("✅ ¡Búsqueda completada!")
    
    # Ejemplo personalizable - cambia estos valores según tus necesidades
    print("\n=== PERSONALIZA TU BÚSQUEDA ===")
    print("Para usar esta función con tus propios parámetros:")
    print("resultados = buscar_archivos_por_nombres_parciales(")
    print("    carpeta='tu/carpeta',")
    print("    nombres_buscados=['termino1', 'termino2'],")
    print("    incluir_subcarpetas=True,")
    print("    case_sensitive=False")
    print(")")
