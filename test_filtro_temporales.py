#!/usr/bin/env python3
"""
Script de prueba para verificar que se filtran correctamente los archivos temporales
"""

from pathlib import Path
import tempfile
import os

def crear_archivos_prueba():
    """Crea archivos de prueba incluyendo algunos temporales"""
    
    # Crear algunos archivos de prueba en la carpeta actual
    archivos_prueba = [
        "archivo_normal.txt",
        "~archivo_temporal.txt",
        "documento.py",
        "~$documento_temporal.docx",
        "~backup_temp.bak"
    ]
    
    print("📁 Creando archivos de prueba...")
    archivos_creados = []
    
    for nombre in archivos_prueba:
        try:
            archivo = Path(nombre)
            archivo.write_text(f"Contenido de {nombre}")
            archivos_creados.append(archivo)
            print(f"✅ Creado: {nombre}")
        except Exception as e:
            print(f"❌ Error creando {nombre}: {e}")
    
    return archivos_creados

def limpiar_archivos_prueba(archivos):
    """Elimina los archivos de prueba"""
    print("\n🧹 Limpiando archivos de prueba...")
    for archivo in archivos:
        try:
            if archivo.exists():
                archivo.unlink()
                print(f"🗑️ Eliminado: {archivo.name}")
        except Exception as e:
            print(f"❌ Error eliminando {archivo.name}: {e}")

def buscar_con_filtro(carpeta, nombres_buscados):
    """Función de búsqueda con filtro de archivos temporales"""
    carpeta = Path(carpeta)
    resultados = {nombre: [] for nombre in nombres_buscados}
    
    print(f"\n🔍 Buscando archivos que contengan: {nombres_buscados}")
    print(f"📁 En carpeta: {carpeta.absolute()}")
    print("-" * 50)
    
    for archivo in carpeta.glob('*'):
        if archivo.is_file():
            # Mostrar todos los archivos encontrados
            print(f"📄 Archivo encontrado: {archivo.name}")
            
            # Verificar si es temporal
            if archivo.name.startswith('~'):
                print(f"   ⚠️  OMITIDO (archivo temporal)")
                continue
            
            # Buscar coincidencias
            for nombre_buscado in nombres_buscados:
                if nombre_buscado.lower() in archivo.name.lower():
                    print(f"   ✅ Coincide con '{nombre_buscado}'")
                    resultados[nombre_buscado].append(archivo)
    
    return resultados

def test_filtro():
    """Prueba principal del filtro"""
    print("🧪 PRUEBA DEL FILTRO DE ARCHIVOS TEMPORALES")
    print("=" * 60)
    
    # Crear archivos de prueba
    archivos_creados = crear_archivos_prueba()
    
    try:
        # Buscar archivos
        terminos_busqueda = ["archivo", "documento", "temp", "txt", "py"]
        resultados = buscar_con_filtro(".", terminos_busqueda)
        
        # Mostrar resultados
        print("\n" + "=" * 60)
        print("📊 RESULTADOS DE LA BÚSQUEDA:")
        
        total_encontrados = 0
        for termino, archivos in resultados.items():
            if archivos:
                print(f"\n'{termino}': {len(archivos)} archivo(s)")
                for archivo in archivos:
                    print(f"  📄 {archivo.name}")
                    total_encontrados += len(archivos)
        
        print(f"\n📈 Total de archivos válidos encontrados: {total_encontrados}")
        
        # Verificar que no se incluyeron archivos temporales
        archivos_temporales_incluidos = []
        for termino, archivos in resultados.items():
            for archivo in archivos:
                if archivo.name.startswith('~'):
                    archivos_temporales_incluidos.append(archivo.name)
        
        if archivos_temporales_incluidos:
            print(f"\n❌ ERROR: Se incluyeron archivos temporales: {archivos_temporales_incluidos}")
        else:
            print(f"\n✅ ÉXITO: No se incluyeron archivos temporales")
            
    finally:
        # Limpiar archivos de prueba
        limpiar_archivos_prueba(archivos_creados)

if __name__ == "__main__":
    test_filtro()
