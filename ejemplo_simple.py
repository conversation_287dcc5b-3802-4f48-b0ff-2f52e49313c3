#!/usr/bin/env python3
"""
Ejemplo simple para revisar archivos con pathlib.Path
"""

from pathlib import Path

def revisar_carpeta(ruta_carpeta):
    """Revisa todos los archivos en una carpeta y subdirectorios"""
    
    carpeta = Path(ruta_carpeta)
    
    # Verificar que la carpeta existe
    if not carpeta.exists():
        print(f"❌ La carpeta '{ruta_carpeta}' no existe")
        return
    
    print(f"📁 Revisando: {carpeta.absolute()}")
    print("-" * 50)
    
    # Método más simple: usar rglob('*') para obtener todo recursivamente
    for item in carpeta.rglob('*'):
        if item.is_file():
            # Información básica del archivo
            tamaño = item.stat().st_size
            print(f"📄 {item}")
            print(f"   Tamaño: {tamaño:,} bytes")
            print(f"   Extensión: {item.suffix}")
            print()

# Ejemplo de uso
if __name__ == "__main__":
    # Revisar la carpeta actual
    revisar_carpeta(".")
    
    # También puedes revisar una carpeta específica:
    # revisar_carpeta("firme")
    # revisar_carpeta("C:/mi_carpeta")
