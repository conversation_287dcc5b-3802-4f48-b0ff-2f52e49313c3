import polars as pl
from pathlib import Path

#%%
def archivos_necesarios(folder: Path, files: list[str]) -> list[Path]:
    """
    Busca archivos que contengan parte del nombre especificado

    Args:
        folder (Path): Carpeta donde buscar
        files (list[str]): Lista de nombres parciales a buscar

    Returns:
        list[Path]: Lista de archivos encontrados
    """
    if not folder.exists():
        raise ValueError(f'Carpeta {folder} no existe')

    encontrados = []
    for file in folder.rglob('*.*'):
        # Buscar coincidencias parciales (case-insensitive)
        for nombre_buscado in files:
            if nombre_buscado.lower() in file.name.lower():
                print(f"✅ Encontrado: {file}")
                print(f"   Buscando: '{nombre_buscado}' -> Archivo: '{file.name}'")
                encontrados.append(file)
                break  # Evitar duplicados si un archivo coincide con múltiples búsquedas

    return encontrados


def buscar_archivos_parciales(folder: Path, nombres_parciales: list[str],
                             case_sensitive: bool = False) -> dict[str, list[Path]]:
    """
    Busca archivos que contengan parte del nombre especificado,
    devolviendo un diccionario organizado por término de búsqueda

    Args:
        folder (Path): Carpeta donde buscar
        nombres_parciales (list[str]): Lista de nombres parciales a buscar
        case_sensitive (bool): Si la búsqueda debe ser sensible a mayúsculas

    Returns:
        dict[str, list[Path]]: Diccionario con término de búsqueda como clave
                              y lista de archivos encontrados como valor
    """
    if not folder.exists():
        raise ValueError(f'Carpeta {folder} no existe')

    resultados = {nombre: [] for nombre in nombres_parciales}

    print(f"🔍 Buscando en: {folder}")
    print(f"📝 Términos de búsqueda: {nombres_parciales}")
    print(f"🔤 Sensible a mayúsculas: {case_sensitive}")
    print("-" * 60)

    for file in folder.rglob('*.*'):
        for nombre_buscado in nombres_parciales:
            # Aplicar case sensitivity según el parámetro
            archivo_nombre = file.name if case_sensitive else file.name.lower()
            busqueda_nombre = nombre_buscado if case_sensitive else nombre_buscado.lower()

            if busqueda_nombre in archivo_nombre:
                print(f"✅ '{nombre_buscado}' encontrado en: {file}")
                resultados[nombre_buscado].append(file)

    # Mostrar resumen
    print("\n" + "=" * 60)
    print("📊 RESUMEN DE BÚSQUEDA:")
    for termino, archivos in resultados.items():
        print(f"'{termino}': {len(archivos)} archivo(s) encontrado(s)")
        for archivo in archivos:
            print(f"  - {archivo}")

    return resultados

#%%
if __name__ == '__main__':
    #%%
    # Ejemplo 1: Búsqueda con la función original (ahora con coincidencias parciales)
    folder = Path(r'C:\Users\<USER>\Desktop\G51_Moka\Potencia\2025\08_Ago_pre')

    # Buscar archivos que contengan estos términos parciales
    nombres_parciales = [
        'InformeSUF',
        'Estimacion_Demanda_Subsistemas_PSUF',
        'Balance_'
    ]

    print("=== MÉTODO 1: archivos_necesarios (coincidencias parciales) ===")
    lista = archivos_necesarios(folder, nombres_parciales)
    print(f"\nTotal encontrados: {len(lista)}")


