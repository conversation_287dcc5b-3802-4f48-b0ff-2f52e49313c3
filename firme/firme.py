import polars as pl
from pathlib import Path

#%%
def archivos_necesarios(folder: Path, files: list[str]) -> list[Path]:
    if not folder.exists():
        raise ValueError(f'Carpeta {folder} no existe')

    encontrados = []
    for file in folder.rglob('*.*'):
        if file.name in files:
            print(file)
            encontrados.append(file)
    return encontrados

#%%
if __name__ == '__main__':
    #%%
    folder = Path(r'C:\Users\<USER>\Desktop\G51_Moka\Potencia\2025\08_Ago_pre')
    lista = archivos_necesarios(folder, ['08_01_2025.csv', '08_02_2025.csv'])
    #%%
    lista

