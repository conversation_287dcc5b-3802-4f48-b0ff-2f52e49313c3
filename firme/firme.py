import polars as pl
from pathlib import Path

#%%
def archivos_necesarios(folder: Path, files: list[str], verbose=False) -> list[
    Path]:
    """
    Busca archivos que contengan parte del nombre especificado

    Args:
        folder (Path): Carpeta donde buscar
        files (list[str]): Lista de nombres parciales a buscar

    Returns:
        list[Path]: Lista de archivos encontrados
    """
    if not folder.exists():
        raise ValueError(f'Carpeta {folder} no existe')

    encontrados = []
    for file in folder.rglob('*.*'):
        # Omitir archivos temporales que empiecen con ~
        if file.name.startswith('~'):
            continue

        # Buscar coincidencias parciales (case-insensitive)
        for nombre_buscado in files:
            if nombre_buscado.lower() in file.name.lower():
                if verbose:
                    print(f"✅ Encontrado: {file}")
                    print(f"   Buscando: '{nombre_buscado}' -> Archivo: '"
                          f"{file.name}'")
                encontrados.append(file)
                break
    return encontrados



#%%
if __name__ == '__main__':
    #%%
    folder = Path(r'C:\Users\<USER>\Desktop\G51_Moka\Potencia\2025\08_Ago_pre')

    # Buscar archivos que contengan estos términos parciales
    nombres_parciales = [
        'InformeSUF',
        'Estimacion_Demanda_Subsistemas_PSUF',
        'Balance_'
    ]

    lista = archivos_necesarios(folder, nombres_parciales, verbose=False)
    #%%
    for x in lista:
        if 'Informe'.lower() in x.name.lower():
            df1 = pl.read_excel(x, sheet_name='Inputs')
            print(df1.head(2))


