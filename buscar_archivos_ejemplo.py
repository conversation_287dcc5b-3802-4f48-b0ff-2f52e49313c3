#!/usr/bin/env python3
"""
Ejemplo simple para buscar archivos por coincidencias parciales en el nombre
"""

from pathlib import Path

def buscar_por_nombres_parciales(carpeta, nombres_buscados):
    """
    Busca archivos que contengan parte del nombre especificado
    
    Args:
        carpeta (str o Path): Ruta de la carpeta donde buscar
        nombres_buscados (list): Lista de nombres parciales a buscar
        
    Returns:
        dict: Diccionario con los resultados organizados por término de búsqueda
    """
    carpeta = Path(carpeta)
    
    if not carpeta.exists():
        print(f"❌ La carpeta '{carpeta}' no existe")
        return {}
    
    print(f"🔍 Buscando en: {carpeta.absolute()}")
    print(f"📝 Buscando archivos que contengan: {nombres_buscados}")
    print("-" * 60)
    
    resultados = {}
    
    # Inicializar el diccionario de resultados
    for nombre in nombres_buscados:
        resultados[nombre] = []
    
    # Buscar en todos los archivos recursivamente
    for archivo in carpeta.rglob('*'):
        if archivo.is_file():
            # Omitir archivos temporales que empiecen con ~
            if archivo.name.startswith('~'):
                continue

            # Verificar cada término de búsqueda
            for nombre_buscado in nombres_buscados:
                # Búsqueda case-insensitive
                if nombre_buscado.lower() in archivo.name.lower():
                    print(f"✅ '{nombre_buscado}' encontrado en: {archivo}")
                    resultados[nombre_buscado].append(archivo)
    
    # Mostrar resumen
    print("\n" + "=" * 60)
    print("📊 RESUMEN:")
    for termino, archivos in resultados.items():
        print(f"'{termino}': {len(archivos)} archivo(s)")
        for archivo in archivos:
            print(f"  📄 {archivo.name} -> {archivo}")
    
    return resultados

def ejemplo_uso():
    """Ejemplos de cómo usar la función"""
    
    # Ejemplo 1: Buscar en la carpeta actual
    print("=== EJEMPLO 1: Carpeta actual ===")
    resultados1 = buscar_por_nombres_parciales(".", ["py", "firme", "init"])
    
    print("\n" + "="*80 + "\n")
    
    # Ejemplo 2: Buscar archivos específicos
    print("=== EJEMPLO 2: Términos más específicos ===")
    resultados2 = buscar_por_nombres_parciales(".", ["ejemplo", "buscar", ".py"])
    
    print("\n" + "="*80 + "\n")
    
    # Ejemplo 3: Buscar en subcarpeta
    print("=== EJEMPLO 3: Buscar en subcarpeta 'firme' ===")
    resultados3 = buscar_por_nombres_parciales("firme", ["firme", "py"])
    
    return resultados1, resultados2, resultados3

if __name__ == "__main__":
    # Ejecutar ejemplos
    ejemplo_uso()
    
    # Ejemplo personalizado - puedes cambiar estos valores
    print("\n" + "="*80)
    print("=== EJEMPLO PERSONALIZADO ===")
    
    # Cambia estos valores según lo que necesites buscar
    mi_carpeta = "."  # Carpeta donde buscar
    mis_terminos = ["py", "txt", "csv"]  # Términos a buscar
    
    mis_resultados = buscar_por_nombres_parciales(mi_carpeta, mis_terminos)
    
    # Acceder a resultados específicos
    print(f"\nArchivos que contienen 'py': {len(mis_resultados.get('py', []))}")
    for archivo in mis_resultados.get('py', []):
        print(f"  - {archivo}")
